package com.salesmobile.ui.home;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.room.Room;

import com.salesmobile.AppDatabase;
import com.salesmobile.R;
import com.salesmobile.databinding.FragmentHomeBinding;

public class HomeFragment extends Fragment {
    private HomeViewModel homeViewModel;
    private AppDatabase db;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        db = AppDatabase.getInstance(requireContext()); // Inicializa aquí
    }

    public View onCreateView(@NonNull LayoutInflater inflater,
                             ViewGroup container, Bundle savedInstanceState) {
        homeViewModel = new ViewModelProvider(this).get(HomeViewModel.class);
        View root = inflater.inflate(R.layout.fragment_home, container, false);



        final TextView tvFecha = root.findViewById(R.id.tv_fecha);
        final TextView tvTotal = root.findViewById(R.id.tv_total);
        final ProgressBar progressBar = root.findViewById(R.id.progressBar);
        final TextView tvTransacciones = root.findViewById(R.id.tv_transacciones);
        final TextView tvTicketMedio = root.findViewById(R.id.tv_ticket_medio);

        homeViewModel.getFecha().observe(getViewLifecycleOwner(), tvFecha::setText);
        homeViewModel.getTotalVentas().observe(getViewLifecycleOwner(), tvTotal::setText);
        homeViewModel.getTransacciones().observe(getViewLifecycleOwner(), tvTransacciones::setText);
        homeViewModel.getTicketMedio().observe(getViewLifecycleOwner(), tvTicketMedio::setText);



        homeViewModel.getIsLoading().observe(getViewLifecycleOwner(), isLoading -> {
            if (isLoading) {
                progressBar.setVisibility(View.VISIBLE);
                tvTotal.setVisibility(View.INVISIBLE);
                tvTransacciones.setVisibility(View.INVISIBLE);
                tvTicketMedio.setVisibility(View.INVISIBLE);
            } else {
                progressBar.setVisibility(View.GONE);
                tvTotal.setVisibility(View.VISIBLE);
                tvTransacciones.setVisibility(View.VISIBLE);
                tvTicketMedio.setVisibility(View.VISIBLE);
            }
        });

        // Obtener la instancia de tu base de datos (ajusta esto según tu implementación)
       // AppDatabase db = Room.databaseBuilder(requireContext(),
        //        AppDatabase.class, "app_database").build();

        homeViewModel.cargarDatosDelDia(db);

        return root;
    }
}