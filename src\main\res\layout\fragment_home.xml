<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_gradient"
    android:padding="16dp"
    tools:context=".ui.home.HomeFragment">

    <!-- Nombre de la sucursal -->
    <TextView
        android:id="@+id/tv_nombre_sucursal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="Sucursal Principal"
        android:textColor="#FFFFFF"
        android:textSize="18sp"
        android:textStyle="bold"
        android:shadowColor="#000000"
        android:shadowDx="1"
        android:shadowDy="1"
        android:shadowRadius="2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <!-- TextView para mostrar la cotización -->
    
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_cotizacion"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:cardBackgroundColor="#4CAF50"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/card_right">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_baseline_attach_money_24"
                app:tint="#FFFFFF"
                android:layout_marginEnd="12dp" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Cotización del Día"
                    android:textColor="#FFFFFF"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_cotizacion"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0.00"
                    android:textColor="#FFFFFF"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginTop="4dp" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_fecha_cotizacion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text=""
                android:textColor="#E8F5E8"
                android:textSize="12sp" />
        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- Tarjeta principal de ventas -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_main"
        android:layout_width="0dp"
        android:layout_height="220dp"
        android:layout_marginBottom="16dp"
        app:cardBackgroundColor="#FFFFFF"
        app:cardCornerRadius="20dp"
        app:cardElevation="6dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_nombre_sucursal">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- Fondo decorativo -->
            <View
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:background="@drawable/bg_card_header"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Icono decorativo -->
            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/ic_sales"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.9"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="#FFFFFF" />

            <!-- Contenido principal -->
            <TextView
                android:id="@+id/tv_titulo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="24dp"
                android:text="VENTAS DEL DÍA"
                android:textColor="#333333"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_fecha"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="4dp"
                android:textColor="#090505"
                android:textSize="16sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_titulo" />

            <TextView
                android:id="@+id/tv_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="65dp"
                android:text="0.00"
                android:textColor="#031404"
                android:textSize="36sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_fecha" />

            <TextView
                android:id="@+id/tv_label_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:text="Total acumulado"
                android:textColor="#2C2222"
                android:textSize="16sp"
                app:layout_constraintBaseline_toBaselineOf="@+id/tv_total"
                app:layout_constraintStart_toEndOf="@+id/tv_total" />

            <!-- Indicador de crecimiento -->
            <LinearLayout
                android:id="@+id/layout_tendencia"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_total">



            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- Tarjeta izquierda (Transacciones) -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_left"
        android:layout_width="0dp"
        android:layout_height="120dp"
        android:layout_marginTop="8dp"
        app:cardBackgroundColor="#2196F3"
        app:cardCornerRadius="16dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toStartOf="@+id/card_right"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/card_main">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_label_transacciones"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="Transacciones"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_transacciones"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="8dp"
                android:text="0"
                android:textColor="#FFFFFF"
                android:textSize="24sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_label_transacciones" />

            <ImageView
                android:id="@+id/ic_transacciones"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:src="@drawable/ic_receipt"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:tint="#FFFFFF" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>

    <!-- Tarjeta derecha (Ticket medio) -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_right"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="8dp"
        app:cardBackgroundColor="#FF9800"
        app:cardCornerRadius="16dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/card_left"
        app:layout_constraintTop_toTopOf="@id/card_left">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_label_ticket"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:text="Ticket medio"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_ticket_medio"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="8dp"
                android:text="0.00"
                android:textColor="#FFFFFF"
                android:textSize="24sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_label_ticket" />

            <ImageView
                android:id="@+id/ic_ticket"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:src="@drawable/ic_average"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:tint="#FFFFFF" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>


    <!-- ProgressBar para carga -->
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <!-- Texto de versión -->
    <TextView
        android:id="@+id/tv_version"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Versión 1.3"
        android:textColor="#666666"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginBottom="8dp"/>


</androidx.constraintlayout.widget.ConstraintLayout>