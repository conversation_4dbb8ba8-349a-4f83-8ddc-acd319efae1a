package com.salesmobile;

import static java.security.AccessController.getContext;

import android.app.AlertDialog;
import android.app.Application;
import android.os.Bundle;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.view.Menu;
import android.widget.Toast;

import com.google.android.material.snackbar.Snackbar;
import com.google.android.material.navigation.NavigationView;

import androidx.core.view.GravityCompat;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import androidx.navigation.ui.AppBarConfiguration;
import androidx.navigation.ui.NavigationUI;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.appcompat.app.AppCompatActivity;

import com.salesmobile.databinding.ActivityMainBinding;
import com.facebook.stetho.Stetho;
import com.salesmobile.ui.client.selectClientFragment;
import com.salesmobile.ui.home.HomeFragment;
import com.salesmobile.config.SharedPreferences;
import com.salesmobile.config.ParametrosConf;
import com.salesmobile.utils.limpiarSesion;

public class MainActivity extends AppCompatActivity {
    private AppDatabase db;
    private AppBarConfiguration mAppBarConfiguration;
    private SharedPreferences sharedPreferences;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        db = AppDatabase.getInstance(this); // Inicializa aquí
        sharedPreferences = new SharedPreferences(this);

        // Inicializar ParametrosConf antes de usar cualquier constante
        ParametrosConf.init(this);

        ActivityMainBinding binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // Configuración básica
        setSupportActionBar(binding.appBarMain.toolbar);

        DrawerLayout drawer = binding.drawerLayout;
        NavigationView navigationView = binding.navView;

        // Configuración del NavController
        NavController navController = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);

        // Configura el AppBar
        mAppBarConfiguration = new AppBarConfiguration.Builder(
                R.id.nav_home,  // Asegúrate que estos IDs coincidan
                R.id.nav_clientFragment,
                R.id.nav_inputProduct,
                R.id.nav_stock,
                R.id.nav_configuracion,
                R.id.nav_close)
                .setOpenableLayout(drawer)
                .build();

        NavigationUI.setupActionBarWithNavController(this, navController, mAppBarConfiguration);


        navigationView.setNavigationItemSelectedListener(item -> {
            int id = item.getItemId();

            // Manejo especial para clientFragment si es necesario
            if (id == R.id.nav_clientFragment) {
                Bundle args = new Bundle();
                args.putBoolean("abiertoDesdeMenu", true);
                navController.navigate(R.id.nav_clientFragment, args);
            }
            // Para todos los demás ítems, incluyendo "Inicio"
            else {
                if (id == R.id.nav_close) {
                    performLogout();
                    return true;
                }

                // Control de acceso para configuración
                if (id == R.id.nav_configuracion) {
                    if (!isConfigurationAccessAllowed()) {
                        showConfigurationAccessDeniedDialog();
                        drawer.closeDrawer(GravityCompat.START);
                        return true;
                    }
                }

                try {
                    navController.navigate(id);
                } catch (IllegalArgumentException e) {
                    Log.e("Navigation", "No se encontró el destino para el ID: " + id);
                    return false;
                }
            }

            drawer.closeDrawer(GravityCompat.START);
            return true;
        });

        if (getIntent() != null && "SELECT_CLIENT".equals(getIntent().getStringExtra("NAVIGATE_TO"))) {
            navController.navigate(R.id.nav_inputProduct);
        }

    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.main, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        return super.onOptionsItemSelected(item);
    }

    @Override
    public boolean onSupportNavigateUp() {
        NavController navController = Navigation.findNavController(this, R.id.nav_host_fragment_content_main);
        return NavigationUI.navigateUp(navController, mAppBarConfiguration)
                || super.onSupportNavigateUp();
    }

    private void performLogout() {
        limpiarSesion.getInstance().performLogout(this, new limpiarSesion.CleanupCallback() {
            @Override
            public void onCleanupComplete(boolean success, String message) {
                if (success) {
                    Log.i("MainActivity", "Logout completed successfully");
                } else {
                    Log.e("MainActivity", "Logout failed: " + message);
                    Toast.makeText(MainActivity.this, "Error durante el logout: " + message, Toast.LENGTH_LONG).show();
                }
            }

            @Override
            public void onCleanupProgress(String operation) {
                Log.d("MainActivity", "Logout progress: " + operation);
            }
        });
    }

    private boolean isConfigurationAccessAllowed() {
        return sharedPreferences.getVerConfig() != 0;
    }

    private void showConfigurationAccessDeniedDialog() {
        new AlertDialog.Builder(this)
                .setTitle("Acceso Denegado")
                .setMessage("No se puede acceder a la pantalla de configuración porque ha sido deshabilitada por el sistema.")
                .setPositiveButton("Aceptar", null)
                .show();
    }

    // Método removido - la consulta de sucursal ahora se hace en selectClientFragment

}