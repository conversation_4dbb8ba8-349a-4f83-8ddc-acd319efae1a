2025-07-30 09:12:45.116  6766-6766  PAYMENT_RESPONSE        com.salesmobile                      D  Respuesta recibida: {"status":"success","data":[{"id":88,"hook_alias":"SLOVT46592","status":"confirmed","url":null,"description":"Compra en Tienda de Nike","fecha_creacion":"Jul 30 2025 10:12AM","fecha_actualizacion":"Jul 30 2025 10:12AM","response_code":"00","response_description":"Pago Exitoso","amount":"133020.00","currency":"GS","installment_number":1,"product_description":null,"date_time":{"date":"2025-07-30 10:12:42.873000","timezone_type":3,"timezone":"UTC"},"ticket_number":"**********","authorization_code":"874072","commerce_name":"ZAVIDORO CORPORATIONS SUC.PY.","branch_name":"NIKE-SOL","bin":"************","merchant_code":"553149","payer_name":null,"payer_lastname":null,"card_last_numbers":2045,"account_type":"TC","created_at":null}]}
2025-07-30 09:12:45.116  6766-6766  WindowOnBackDispatcher  com.salesmobile                      W  sendCancelIfRunning: isInProgress=falsecallback=android.app.Dialog$$ExternalSyntheticLambda2@ba20b06
2025-07-30 09:12:45.119  6766-6766  View                    com.salesmobile                      D  [Warning] assignParent to null: this = DecorView@685b8e0[MainActivity]
2025-07-30 09:12:45.138  6766-12739 BLASTBufferQueue        com.salesmobile                      D  [VRI[MainActivity]#17](f:0,a:1) destructor()
2025-07-30 09:12:45.138  6766-12739 BufferQueueConsumer     com.salesmobile                      D  [VRI[MainActivity]#17(BLAST Consumer)17](id:1a6e00000011,api:0,p:-1,c:6766) disconnect
2025-07-30 09:12:45.141  6766-6766  AndroidRuntime          com.salesmobile                      D  Shutting down VM
2025-07-30 09:12:45.147  6766-6766  AndroidRuntime          com.salesmobile                      E  FATAL EXCEPTION: main (Ask Gemini)
Process: com.salesmobile, PID: 6766
java.lang.StringIndexOutOfBoundsException: length=6; index=-2
	at java.lang.String.substring(String.java:2485)
	at com.salesmobile.ui.factura.FacturaManager.construirFacturaJson(FacturaManager.java:283)
	at com.salesmobile.ui.factura.FacturaManager.grabarFactura(FacturaManager.java:70)
	at com.salesmobile.ui.client.selectClientFragment.grabarFactura(selectClientFragment.java:989)
	at com.salesmobile.ui.client.selectClientFragment.handlePaymentSuccess(selectClientFragment.java:955)
	at com.salesmobile.ui.client.selectClientFragment.lambda$verificarEstadoPago$13$com-salesmobile-ui-client-selectClientFragment(selectClientFragment.java:876)
	at com.salesmobile.ui.client.selectClientFragment$$ExternalSyntheticLambda5.onResponse(D8$$SyntheticClass:0)
	at com.android.volley.toolbox.JsonRequest.deliverResponse(JsonRequest.java:100)
	at com.android.volley.ExecutorDelivery$ResponseDeliveryRunnable.run(ExecutorDelivery.java:102)
	at android.os.Handler.handleCallback(Handler.java:958)
	at android.os.Handler.dispatchMessage(Handler.java:99)
	at android.os.Looper.loopOnce(Looper.java:222)
	at android.os.Looper.loop(Looper.java:314)
	at android.app.ActivityThread.main(ActivityThread.java:8716)
	at java.lang.reflect.Method.invoke(Native Method)
	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:565)
	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1081)